from flask import request, url_for
from pylti1p3.dynamic_registration import DynamicRegistration, generate_key_pair
from typing import Any, Dict
import os
import json

class FlaskDynamicRegistration(DynamicRegistration):

    logo_file = 'logo.png'

    def __init__(self, config_path, request=None, registration_data=None):
        super().__init__()
        self.config_path = config_path
        self.request = request
        self.registration_data = registration_data
        

    def _load_config(self) -> Dict:
        """Load the configuration from JSON file"""
        if not os.path.exists(self.config_path):
            return {}
        with open(self.config_path, 'r') as f:
            return json.load(f)

    def _save_config(self, config: Dict):
        """Save the configuration to JSON file"""
        with open(self.config_path, 'w') as f:
            json.dump(config, f, indent=4)    
    
    # Changes for registration_data
    def _get_arg(self, key):
        if self.registration_data:
            return self.registration_data.get(key)
        if self.request:
            return self.request.args.get(key)
        return None
    
    def _get_host(self):
        if self.registration_data:
            return self.registration_data.get('host')
        return self.request.host
    
    def _get_url_root(self):
        if self.registration_data:
            return self.registration_data.get('url_root')
        return self.request.url_root

    def get_initiate_login_uri(self) -> str:
        print(self.build_absolute_uri(url_for('login')))
        return self.build_absolute_uri(url_for('login'))

    def get_jwks_uri(self) -> str:
        #return self.build_absolute_uri(url_for('get_jwks')) 
        return 'https://doc4g.zedbud.me/jwks'
     
    def get_redirect_uris(self) -> list[str]:
        return [self.get_target_link_uri()]

    def get_domain(self) -> str:
        #return request.host
        return self._get_host()
    
    def get_target_link_uri(self) -> str:
        return self.build_absolute_uri(url_for('launch'))

    def get_logo_uri(self) -> str:
        return self.build_absolute_uri(url_for('static', filename=self.logo_file))

    def get_openid_configuration_endpoint(self):
        #return request.args.get('openid_configuration')
        return self._get_arg('openid_configuration')

    def get_registration_token(self):
        #return request.args.get('registration_token')
        return self._get_arg('registration_token')

    def get_platform_name(self, openid_configuration: Dict[str, Any]) -> str:
        return openid_configuration.get(
            "https://purl.imsglobal.org/spec/lti-platform-configuration", {}
        ).get("product_family_code", '')

    def complete_registration(self, openid_configuration: Dict[str, Any], openid_registration: Dict[str, Any]):
        config = self._load_config()
        issuer = openid_configuration['issuer']
        
        # Generate new key pair if needed
        private_key, public_key = generate_key_pair()
        
        tool_spec = "https://purl.imsglobal.org/spec/lti-tool-configuration"
        deployment_ids = []
    
        if tool_spec in openid_registration:
            tool_config = openid_registration[tool_spec]
            # Try different possible keys for deployment_id
            if 'deployment_id' in tool_config:
                deployment_ids = [tool_config['deployment_id']]
            elif 'lti_deployment_id' in tool_config:
                deployment_ids = [tool_config['lti_deployment_id']]       


        # Create issuer configuration
        issuer_config = {
            "default": True,  # First registration is default
            "client_id": openid_registration['client_id'],
            "auth_login_url": openid_configuration['authorization_endpoint'],
            "auth_token_url": openid_configuration['token_endpoint'],
            "auth_audience": openid_configuration['token_endpoint'],
            "key_set_url": openid_configuration['jwks_uri'],
            "key_set": None,
            "private_key": private_key,
            "public_key": public_key,
            "deployment_ids": deployment_ids
        }

        # Add to existing issuer or create new
        if issuer in config:
            # Set default to False for existing configs
            for existing in config[issuer]:
                existing['default'] = False
            config[issuer].append(issuer_config)
        else:
            config[issuer] = [issuer_config]

        self._save_config(config)
        return issuer_config

    def build_absolute_uri(self, relative_url: str) -> str:
        #return request.url_root.rstrip('/') + relative_url
        return self._get_url_root().rstrip('/') + relative_url