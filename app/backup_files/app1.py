#import datetime
import os
import pprint
import pyrebase

from tempfile import mkdtemp
from flask import Flask, jsonify, render_template, session, request, redirect #, url_for, 
from flask_caching import Cache
#from werkzeug.exceptions import Forbidden
from pylti1p3.contrib.flask import FlaskOIDCLogin, FlaskMessageLaunch, FlaskRequest, FlaskCacheDataStorage
#from pylti1p3.deep_link_resource import DeepLinkResource
#from pylti1p3.grade import Grade
#from pylti1p3.lineitem import LineItem
from pylti1p3.tool_config import ToolConfJsonFile
from pylti1p3.registration import Registration


class ReverseProxied:
    def __init__(self, app):
        self.app = app

    def __call__(self, environ, start_response):
        scheme = environ.get('HTTP_X_FORWARDED_PROTO')
        if scheme:
            environ['wsgi.url_scheme'] = scheme
        return self.app(environ, start_response)


app = Flask('stunlo-tool', template_folder='templates', static_folder='static')
app.wsgi_app = ReverseProxied(app.wsgi_app)

firebase_config = {
    "apiKey": "AIzaSyBKem6ntbK_OIkSsWm_5l8mS8Ts3KDt5Yg",
    "authDomain": "lit-auth-e29c2.firebaseapp.com",
    "projectId": "lit-auth-e29c2",
    "storageBucket": "lit-auth-e29c2.firebasestorage.app",
    "messagingSenderId": "977155575300",
    "appId": "1:977155575300:web:c15bd100a33846c2c3af51",
    "measurementId": "G-P4Z9VPT99P",
    "databaseURL": ""
}
firebase = pyrebase.initialize_app(firebase_config)
auth = firebase.auth()


config = {
    "DEBUG": True,
    "ENV": "development",
    "CACHE_TYPE": "simple",
    "CACHE_DEFAULT_TIMEOUT": 600,
    "SECRET_KEY": "replace-me",
    "SESSION_TYPE": "filesystem",
    "SESSION_FILE_DIR": mkdtemp(),
    "SESSION_COOKIE_NAME": "stunlo-tool-sessionid",
    "SESSION_COOKIE_HTTPONLY": True,
    "SESSION_COOKIE_SECURE": False,   # should be True in case of HTTPS usage (production)
    "SESSION_COOKIE_SAMESITE": None,  # should be 'None' in case of HTTPS usage (production)
    "DEBUG_TB_INTERCEPT_REDIRECTS": False
}
app.config.from_mapping(config)
cache = Cache(app)

PAGE_TITLE = 'Stunlo Tool'

def get_lti_config_path():
    return os.path.join(app.root_path, '..', 'configs', 'app.json')


def get_launch_data_storage():
    return FlaskCacheDataStorage(cache)


def get_jwk_from_public_key(key_name):
    key_path = os.path.join(app.root_path, '..', 'configs', key_name)
    f = open(key_path, 'r')
    key_content = f.read()
    jwk = Registration.get_jwk(key_content)
    f.close()
    return jwk


@app.route('/login', methods=['POST', 'GET'])
def index():
    print("Reached /login/ route")
    flask_request = FlaskRequest()
    target_link_uri = flask_request.get_param('target_link_uri')
    print(flask_request)

    if('user' in session):
        print("User is already logged in")
        return 'Hi, {}'.format(session['user'])
    
    return render_template('login.html', target_link_uri=target_link_uri)

@app.route('/loginredirect', methods=['POST', 'GET'])
def trylogin():
    email = request.form.get('email')
    print(email)
    password = request.form.get('password')
    print(password)
    target_link_uri = request.form.get('target_link_uri')  # Retrieve target_link_uri from the form
    print(target_link_uri)

    if request.method == 'POST' and email and password:
        print("Received POST request with email and password")
        if not target_link_uri:
            raise Exception('Missing "target_link_uri" param')
        try:
            user = auth.sign_in_with_email_and_password(email, password)
            print(user)
            session['user'] = user
            return redirect(target_link_uri)
        except:
            return 'Invalid credentials'
    return render_template('login.html')
        

@app.route('/logout')
def logout():
    session.pop('user')
    return redirect('/login/')

@app.route('/lti/login', methods=['GET', 'POST'])
def login():
    tool_conf = ToolConfJsonFile(get_lti_config_path())
    launch_data_storage = get_launch_data_storage()

    flask_request = FlaskRequest()
    target_link_uri = flask_request.get_param('target_link_uri')
    if not target_link_uri:
        raise Exception('Missing "target_link_uri" param')

    oidc_login = FlaskOIDCLogin(flask_request, tool_conf, launch_data_storage=launch_data_storage)
    return oidc_login\
        .enable_check_cookies()\
        .redirect(target_link_uri)


@app.route('/launch', methods=['POST'])
def launch():
    tool_conf = ToolConfJsonFile(get_lti_config_path())
    flask_request = FlaskRequest()
    launch_data_storage = get_launch_data_storage()
    message_launch = FlaskMessageLaunch(flask_request, tool_conf, launch_data_storage=launch_data_storage)
    message_launch_data = message_launch.get_launch_data()
    pprint.pprint(message_launch_data)

    return render_template('stunlo.html')


@app.route('/jwks', methods=['GET'])
def get_jwks():
    tool_conf = ToolConfJsonFile(get_lti_config_path())
    return jsonify({'keys': tool_conf.get_jwks()})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9001)
