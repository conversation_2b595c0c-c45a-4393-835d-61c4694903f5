#import datetime
import os
import pprint
import uuid
import json

from tempfile import mkdtemp
from flask import Flask, jsonify, render_template, request, url_for
from flask_caching import Cache
#from werkzeug.exceptions import Forbidden
from pylti1p3.contrib.flask import <PERSON>laskOIDCLogin, FlaskMessageLaunch, FlaskRequest, FlaskCacheDataStorage
#from pylti1p3.deep_link_resource import DeepLinkResource
#from pylti1p3.grade import Grade
#from pylti1p3.lineitem import LineItem
from pylti1p3.tool_config import ToolConfJsonFile
from pylti1p3.registration import Registration


class ReverseProxied:
    def __init__(self, app):
        self.app = app

    def __call__(self, environ, start_response):
        scheme = environ.get('HTTP_X_FORWARDED_PROTO')
        if scheme:
            environ['wsgi.url_scheme'] = scheme
        return self.app(environ, start_response)


app = Flask('stunlo-tool', template_folder='templates', static_folder='static')
app.wsgi_app = ReverseProxied(app.wsgi_app)

config = {
    "DEBUG": True,
    "ENV": "development",
    "CACHE_TYPE": "simple",
    "CACHE_DEFAULT_TIMEOUT": 600,
    "SECRET_KEY": "replace-me",
    "SESSION_TYPE": "filesystem",
    "SESSION_FILE_DIR": mkdtemp(),
    "SESSION_COOKIE_NAME": "stunlo-tool-sessionid",
    "SESSION_COOKIE_HTTPONLY": True,
    "SESSION_COOKIE_SECURE": False,   # should be True in case of HTTPS usage (production)
    "SESSION_COOKIE_SAMESITE": None,  # should be 'None' in case of HTTPS usage (production)
    "DEBUG_TB_INTERCEPT_REDIRECTS": False
}
app.config.from_mapping(config)
cache = Cache(app)

PAGE_TITLE = 'Stunlo Tool'

def get_lti_config_path():
    return os.path.join(app.root_path, '..', 'configs', 'app.json')


def get_launch_data_storage():
    return FlaskCacheDataStorage(cache)


def get_jwk_from_public_key(key_name):
    key_path = os.path.join(app.root_path, '..', 'configs', key_name)
    f = open(key_path, 'r')
    key_content = f.read()
    jwk = Registration.get_jwk(key_content)
    f.close()
    return jwk


@app.route('/login', methods=['GET', 'POST'])
def login():
    tool_conf = ToolConfJsonFile(get_lti_config_path())
    launch_data_storage = get_launch_data_storage()

    flask_request = FlaskRequest()
    target_link_uri = flask_request.get_param('target_link_uri')
    if not target_link_uri:
        raise Exception('Missing "target_link_uri" param')

    oidc_login = FlaskOIDCLogin(flask_request, tool_conf, launch_data_storage=launch_data_storage)
    return oidc_login\
        .enable_check_cookies()\
        .redirect(target_link_uri)


@app.route('/launch', methods=['POST'])
def launch():
    tool_conf = ToolConfJsonFile(get_lti_config_path())
    flask_request = FlaskRequest()
    launch_data_storage = get_launch_data_storage()
    message_launch = FlaskMessageLaunch(flask_request, tool_conf, launch_data_storage=launch_data_storage)
    message_launch_data = message_launch.get_launch_data()
    pprint.pprint(message_launch_data)

    #return render_template('stunlo.html')
    return '<h1>Sent to Stunlo!</h1>'

"""
@app.route('/redirectplus', methods=['GET'])
def redirectplus():
    baseURL = request.args.get('baseURL')
    courseID = request.args.get('courseID')
    print(f"baseURL: {baseURL}")
    print(f"courseID: {courseID}")

    return render_template('redirectplus.html', baseURL=baseURL, courseID=courseID)

@app.route('/senddata', methods=['POST'])
def senddata():
    baseURL = request.form.get('baseURL')
    courseID = request.form.get('courseID')
    assignmentID = request.form.get('assignmentID')
    print(f"baseURL: {baseURL}")
    print(f"courseID: {courseID}")
    print(f"assignmentID: {assignmentID}")

    return 'Sent successfully'
"""
#@app.route('/register', methods=['GET'])
#def tool_conf():
#    tool_conf = ToolConfJsonFile(get_lti_config_path())
#    return jsonify(tool_conf.get_tool_conf())

@app.route('/jwks', methods=['GET'])
def get_jwks():
    tool_conf = ToolConfJsonFile(get_lti_config_path())
    return jsonify({'keys': tool_conf.get_jwks()})

# Dynamic Registration endpoints
@app.route('/.well-known/lti-tool-configuration', methods=['GET'])
def lti_tool_configuration():
    """
    Endpoint for dynamic registration discovery
    Returns LTI tool configuration details according to the spec
    """
    tool_conf = ToolConfJsonFile(get_lti_config_path())
    
    # Generate absolute URLs for the required endpoints
    login_url = url_for('login', _external=True)
    launch_url = url_for('launch', _external=True)
    jwks_url = url_for('get_jwks', _external=True)
    
    # Base configuration
    config = {
        "application_type": "web",
        "response_types": ["id_token"],
        "grant_types": ["implicit", "client_credentials"],
        "initiate_login_uri": login_url,
        "redirect_uris": [launch_url],
        "jwks_uri": jwks_url,
        "token_endpoint_auth_method": "private_key_jwt",
        "client_name": "Stunlo LTI Tool",
        "client_uri": request.host_url,
        "scope": "openid",
        "token_endpoint_auth_method": "private_key_jwt",
        "response_modes": ["form_post"],
        "lti_tool_configuration": {
            "version": "1.3.0",
            "deployment_id": "", # To be supplied by the platform
            "target_link_uri": launch_url,
            "domain": request.host,
            "description": "Stunlo LTI 1.3 Tool",
            "claims": ["iss", "sub", "name", "given_name", "family_name"],
            "messages": [
                {
                    "type": "LtiResourceLinkRequest",
                    "target_link_uri": launch_url,
                    "label": "Stunlo LTI Tool"
                }
            ]
        }
    }
    
    return jsonify(config)

@app.route('/registration', methods=['POST'])
def register():
    """
    Endpoint for receiving dynamic registration requests from platforms
    """
    registration_token = request.headers.get('Authorization', '').replace('Bearer ', '')
    registration_data = request.json
    
    if not registration_token or not registration_data:
        return jsonify({"error": "Invalid registration request"}), 400
    
    try:
        # Create a client ID - could be based on platform info or random
        client_id = str(uuid.uuid4())
        
        # Extract platform info
        issuer = registration_data.get('issuer')
        auth_login_url = registration_data.get('auth_login_url')
        auth_token_url = registration_data.get('token_url')
        key_set_url = registration_data.get('jwks_uri')
        
        # Read existing config
        config_path = get_lti_config_path()
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check if this issuer already exists
        if issuer not in config:
            config[issuer] = []
        
        # Add the new registration
        deployment_id = registration_data.get('deployment_id', 'default-deployment')
        
        new_registration = {
            "default": len(config[issuer]) == 0,  # First registration for this issuer becomes default
            "client_id": client_id,
            "auth_login_url": auth_login_url,
            "auth_token_url": auth_token_url,
            "key_set_url": key_set_url,
            "key_set": None,
            "private_key_file": "private.key",
            "public_key_file": "public.key",
            "deployment_ids": [deployment_id]
        }
        
        config[issuer].append(new_registration)
        
        # Save updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        
        # Return the registration response
        registration_response = {
            "client_id": client_id,
            "registration_client_uri": url_for('registration_view', client_id=client_id, _external=True),
            "application_type": "web",
            "response_types": ["id_token"],
            "grant_types": ["implicit", "client_credentials"],
            "initiate_login_uri": url_for('login', _external=True),
            "redirect_uris": [url_for('launch', _external=True)],
            "client_name": "Stunlo LTI Tool",
            "jwks_uri": url_for('get_jwks', _external=True),
            "token_endpoint_auth_method": "private_key_jwt",
            "scope": "openid"
        }
        
        return jsonify(registration_response)
    
    except Exception as e:
        print(f"Registration error: {str(e)}")
        return jsonify({"error": "Registration failed", "details": str(e)}), 500

@app.route('/registration/<client_id>', methods=['GET'])
def registration_view(client_id):
    """
    View an existing tool registration by client_id
    """
    try:
        # Read config
        config_path = get_lti_config_path()
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Find the registration with the given client_id
        for issuer, registrations in config.items():
            for reg in registrations:
                if reg.get('client_id') == client_id:
                    # Return the registration details
                    return jsonify({
                        "client_id": client_id,
                        "application_type": "web",
                        "response_types": ["id_token"],
                        "grant_types": ["implicit", "client_credentials"],
                        "initiate_login_uri": url_for('login', _external=True),
                        "redirect_uris": [url_for('launch', _external=True)],
                        "client_name": "Stunlo LTI Tool",
                        "jwks_uri": url_for('get_jwks', _external=True),
                        "token_endpoint_auth_method": "private_key_jwt",
                        "scope": "openid"
                    })
        
        # If no registration was found
        return jsonify({"error": f"No registration found for client_id {client_id}"}), 404
    
    except Exception as e:
        return jsonify({"error": "Error retrieving registration", "details": str(e)}), 500

@app.route('/registration/<client_id>', methods=['PUT'])
def registration_update(client_id):
    """
    Update an existing tool registration
    """
    registration_token = request.headers.get('Authorization', '').replace('Bearer ', '')
    updated_data = request.json
    
    if not registration_token or not updated_data:
        return jsonify({"error": "Invalid update request"}), 400
    
    try:
        # Read config
        config_path = get_lti_config_path()
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Find and update the registration
        found = False
        for issuer, registrations in config.items():
            for i, reg in enumerate(registrations):
                if reg.get('client_id') == client_id:
                    # Update relevant fields (adjust as needed)
                    if 'auth_login_url' in updated_data:
                        reg['auth_login_url'] = updated_data['auth_login_url']
                    if 'auth_token_url' in updated_data:
                        reg['auth_token_url'] = updated_data['token_url']
                    if 'key_set_url' in updated_data:
                        reg['key_set_url'] = updated_data['jwks_uri']
                    
                    config[issuer][i] = reg
                    found = True
                    break
        
        if not found:
            return jsonify({"error": f"No registration found for client_id {client_id}"}), 404
        
        # Save updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        
        # Return the updated registration
        return jsonify({
            "client_id": client_id,
            "application_type": "web",
            "response_types": ["id_token"],
            "grant_types": ["implicit", "client_credentials"],
            "initiate_login_uri": url_for('login', _external=True),
            "redirect_uris": [url_for('launch', _external=True)],
            "client_name": "Stunlo LTI Tool",
            "jwks_uri": url_for('get_jwks', _external=True),
            "token_endpoint_auth_method": "private_key_jwt",
            "scope": "openid"
        })
    
    except Exception as e:
        return jsonify({"error": "Error updating registration", "details": str(e)}), 500

@app.route('/registration/<client_id>', methods=['DELETE'])
def registration_delete(client_id):
    """
    Delete an existing tool registration
    """
    registration_token = request.headers.get('Authorization', '').replace('Bearer ', '')
    
    if not registration_token:
        return jsonify({"error": "Invalid delete request"}), 400
    
    try:
        # Read config
        config_path = get_lti_config_path()
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Find and delete the registration
        found = False
        for issuer, registrations in config.items():
            for i, reg in enumerate(registrations):
                if reg.get('client_id') == client_id:
                    # Remove this registration
                    del config[issuer][i]
                    
                    # If this was the default and there are other registrations, make another one default
                    if reg.get('default', False) and config[issuer]:
                        config[issuer][0]['default'] = True
                    
                    # If no more registrations for this issuer, remove the issuer
                    if not config[issuer]:
                        del config[issuer]
                    
                    found = True
                    break
            if found:
                break
        
        if not found:
            return jsonify({"error": f"No registration found for client_id {client_id}"}), 404
        
        # Save updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        
        return jsonify({"message": "Registration deleted successfully"})
    
    except Exception as e:
        return jsonify({"error": "Error deleting registration", "details": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9001)
