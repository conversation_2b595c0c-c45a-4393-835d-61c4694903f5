import json
import os
import typing as t
from pylti1p3.tool_config.dict import TIssConf, TJsonData, ToolConfDict


class ToolConfigJson(ToolConfDict):
    def __init__(self, config_file: str):
        """
        Example config format:
        {
            "iss1": [{
                "default": true,
                "client_id": "client_id1",
                "auth_login_url": "auth_login_url1",
                "auth_token_url": "auth_token_url1",
                "auth_audience": null,
                "key_set_url": "key_set_url1",
                "key_set": null,
                "private_key": "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----",
                "public_key": "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----",
                "deployment_ids": ["deployment_id1"]
            }],
            "iss2": [ ... ]
        }
        """
        if not os.path.isfile(config_file):
            raise Exception("LTI tool config file not found: " + config_file)
        self._configs_dir = os.path.dirname(config_file)

        with open(config_file, encoding="utf-8") as cfg:
            iss_conf_dict: TJsonData = json.loads(cfg.read())
            super().__init__(iss_conf_dict)

        for iss in iss_conf_dict:
            if isinstance(iss_conf_dict[iss], list):
                for iss_conf in iss_conf_dict[iss]:
 #                   print("here: ", iss_conf)
                    client_id = t.cast(TIssConf, iss_conf).get("client_id")
                    self._process_iss_conf_item(
                        t.cast(TIssConf, iss_conf), iss, client_id
                    )
            else:
 #               print("else: ", iss_conf_dict)
                self._process_iss_conf_item(t.cast(TIssConf, iss_conf_dict[iss]), iss)

    def _process_iss_conf_item(
        self, iss_conf: TIssConf, iss: str, client_id: t.Optional[str] = None
    ):
        """Process issuer configuration reading keys directly from config"""
 #       print("in func: ", iss_conf)
        private_key = iss_conf.get("private_key")
        if not private_key:
            raise Exception("iss config error: private_key not found")
        
        self.set_private_key(iss, private_key, client_id=client_id)

        public_key = iss_conf.get("public_key")
        if public_key:
            self.set_public_key(iss, public_key, client_id=client_id)
            
            
class FlatToolConfigJson(ToolConfDict):
    def __init__(self, config_file: str):
        """
        Example config format:
        [
            {
                "iss": "issuer"
                "default": true,
                "client_id": "client_id1",
                "auth_login_url": "auth_login_url1",
                "auth_token_url": "auth_token_url1",
                "auth_audience": null,
                "key_set_url": "key_set_url1",
                "key_set": null,
                "private_key": "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----",
                "public_key": "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----",
                "deployment_ids": ["deployment_id1"]
            },
            {
                "iss: "issuer2"
            }
        ]
        """
        if not os.path.isfile(config_file):
            raise Exception("LTI tool config file not found: " + config_file)
        self._configs_dir = os.path.dirname(config_file)

        with open(config_file, encoding="utf-8") as cfg:
            config_data = json.loads(cfg.read())
            
            # Convert flat list to the nested structure expected by ToolConfDict
            iss_conf_dict: TJsonData = {}
            for item in config_data:
                iss = item.get("iss")
                if not iss:
                    raise Exception("iss config error: iss field not found")
                
                # Create a copy without the iss field
                iss_conf = {k: v for k, v in item.items() if k != "iss"}
                
                if iss not in iss_conf_dict:
                    iss_conf_dict[iss] = []
                iss_conf_dict[iss].append(iss_conf)
            
            super().__init__(iss_conf_dict)

        # Process each item to set up keys
        for item in config_data:
            iss = item.get("iss")
            client_id = item.get("client_id")
            self._process_iss_conf_item(t.cast(TIssConf, item), iss, client_id)

    def _process_iss_conf_item(
        self, iss_conf: TIssConf, iss: str, client_id: t.Optional[str] = None
    ):
        """Process issuer configuration reading keys directly from config"""
        private_key = iss_conf.get("private_key")
        if not private_key:
            raise Exception("iss config error: private_key not found")
        
        self.set_private_key(iss, private_key, client_id=client_id)

        public_key = iss_conf.get("public_key")
        if public_key:
            self.set_public_key(iss, public_key, client_id=client_id)
            