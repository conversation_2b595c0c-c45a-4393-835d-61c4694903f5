<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    
    <style>
        :root {
            --primary-color: #4CAF50;
            --bg-color: #f4f7f9;
            --form-bg: #fff;
            --border-color: #ddd;
            --font-color: #333;
        }

        body {
            background-color: var(--bg-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--font-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .login-container {
            background-color: var(--form-bg);
            padding: 30px 40px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            box-sizing: border-box;
        }

        h2 {
            text-align: center;
            margin-bottom: 25px;
            color: var(--primary-color);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        input[type="email"],
        input[type="password"],
        button {
            width: 100%;
            box-sizing: border-box;
        }

        input[type="email"],
        input[type="password"] {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 16px;
        }

        button {
            padding: 12px;
            background-color: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #45a049;
        }

        .error {
            color: red;
            background-color: #ffe5e5;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>

    <div class="login-container">
        <h2>Stunlo Login</h2>
        {% if error %}
            <div class="error">{{ error }}</div>
        {% endif %}

        <form action="{{ url_for('authenticate', launch_id=launch_id) }}" method="post">
            <div class="form-group">
                <label for="username">Email:</label>
                <input type="email" id="username" name="username" placeholder="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" placeholder="••••••••" required>
            </div>

            <button type="submit">Log In</button>
        </form>
    </div>

</body>
</html>
