<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title><PERSON><PERSON><PERSON></title>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>

  <style>
    :root {
      --primary-color: #4CAF50;
      --bg-color: #f4f7f9;
      --form-bg: #fff;
      --border-color: #ddd;
      --font-color: #333;
    }

    body {
      background-color: var(--bg-color);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: var(--font-color);
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }

    .login-container {
      background-color: var(--form-bg);
      padding: 30px 40px;
      border-radius: 10px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 420px;
      box-sizing: border-box;
      display: none;
    }

    .action-container {
      display: none;
      text-align: center;
    }

    .form-group {
      margin-bottom: 20px;
    }

    h2 {
      text-align: center;
      margin-bottom: 25px;
      color: var(--primary-color);
    }

    label {
      font-weight: 600;
      margin-bottom: 8px;
      display: block;
    }

    input,
    button {
      width: 100%;
      box-sizing: border-box;
    }

    input[type="email"],
    input[type="password"] {
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      font-size: 16px;
    }

    button {
      padding: 12px;
      background-color: var(--primary-color);
      color: #fff;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-top: 10px;
    }

    button:hover {
      background-color: #45a049;
    }

    .error {
      color: red;
      background-color: #ffe5e5;
      padding: 10px;
      border-radius: 6px;
      margin-bottom: 15px;
      text-align: center;
    }

    .loading {
      color: #004085;
      background-color: #cce5ff;
      padding: 10px;
      border-radius: 6px;
      margin-top: 10px;
      text-align: center;
      display: none;
    }

    .success-notification {
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #4CAF50;
      color: white;
      padding: 15px 25px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      z-index: 1000;
      text-align: center;
      animation: slideDown 0.3s ease-out, fadeOut 0.5s ease-in 1s forwards;
      display: none;
    }
    
    @keyframes slideDown {
      from { transform: translate(-50%, -50px); opacity: 0; }
      to { transform: translate(-50%, 0); opacity: 1; }
    }
    
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
  </style>
</head>
<body>

  <!-- Success Notification -->
  <div id="success-notification" class="success-notification">
    <h3>Sent to Stunlo!</h3>
  </div>

  <!-- Loader -->
  <div id="loading-screen" style="text-align: center; font-size: 18px;">Checking authentication...</div>

  <!-- Login Form -->
  <div class="login-container" id="login-container">
    <h2>Stunlo Login</h2>
    <div id="error-message" class="error" style="display: none;"></div>

    <form id="login-form" onsubmit="return false;">
      <div class="form-group">
        <label for="username">Email:</label>
        <input type="email" id="username" required />
      </div>

      <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" required />
      </div>

      <button type="submit" id="login-button">Log In</button>
      <div id="loading" class="loading">Authenticating...</div>
    </form>
  </div>

  <!-- Share / Switch User UI -->
  <div class="action-container" id="action-container">
    <h2>Welcome to Stunlo!, <span id="user-email">User</span></h2>
    <button id="share-button">Share to Stunlo</button>
    <button id="switch-button">Switch User</button>

    <div id="share-loading" class="loading" style="display: none;">Sharing to Stunlo...</div>
  </div>

  <script>
    const isDeepLinkLaunch = "{{ is_deep_link_launch }}";
    const parsedIsDeepLinkLaunch = (isDeepLinkLaunch === 'True');

    const isMessageLaunch = "{{ is_message_launch }}";
    const parsedIsMessageLaunch = (isMessageLaunch === 'True');

    const firebaseConfig = {
      apiKey: "AIzaSyBKem6ntbK_OIkSsWm_5l8mS8Ts3KDt5Yg",
      authDomain: "lit-auth-e29c2.firebaseapp.com",
      projectId: "lit-auth-e29c2",
      storageBucket: "lit-auth-e29c2.firebasestorage.app",
      messagingSenderId: "977155575300",
      appId: "1:977155575300:web:c15bd100a33846c2c3af51",
      measurementId: "G-P4Z9VPT99P"
    };

    firebase.initializeApp(firebaseConfig);

    window.addEventListener('DOMContentLoaded', () => {
      const shareButton = document.getElementById('share-button');
      if (parsedIsMessageLaunch) {
        if (parsedIsDeepLinkLaunch) {
          shareButton.textContent = 'Add to Assignment';
        } else {
          shareButton.textContent = 'Share to Stunlo'; // Default
        }
      } else {
        shareButton.textContent = 'Register';
      }
    });

    async function sendIdTokenToServer(user) {
      const idToken = await user.getIdToken();
      let response;
      try {
        if (parsedIsMessageLaunch) {
          response = await fetch('/launch-auth', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ 
              idToken,
              launch_id: '{{ launch_id }}'
            })
          });
        } else {
          response = await fetch('/register-auth', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ 
              idToken,
              reg_id: '{{ reg_id }}'
            })
          });
        }

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText || "Server error");
        }

        const contentType = response.headers.get('Content-Type') || '';
        if (contentType.includes('application/json')) {
          const jsonResponse = await response.json();
          if (jsonResponse.success) {
            showSuccessNotification();
            //return jsonResponse;
          }
        } else if (contentType.includes('text/html')) {
          // Handle HTML response (for deep linking)
          const html = await response.text();
          document.open();
          document.write(html);
          document.close();
        }
      } catch (error) {
        console.error("Error:", error);
        throw error;
      }
    }
/*      if (contentType.includes('text/html')) {
        const html = await response.text();
        document.open();
        document.write(html);
        document.close();
      } else {

        //window.close(); // fallback: just close after sharing
      };
    }*/

    function showSuccessNotification() {  
      const notification = document.getElementById('success-notification');
      notification.style.display = 'block';
      // Hide notification after animation completes
      setTimeout(() => {
        notification.style.display = 'none';
        setTimeout(() => {
          window.close();
        }, 500);
      }, 2000);  
    }

    firebase.auth().onAuthStateChanged(async (user) => {
      const loginContainer = document.getElementById("login-container");
      const actionContainer = document.getElementById("action-container");
      const loadingScreen = document.getElementById("loading-screen");
      const userEmailSpan = document.getElementById("user-email");

      loadingScreen.style.display = "none";

      console.log("User:", user);

      if (user) {

        userEmailSpan.textContent = user.email || "";
        actionContainer.style.display = "block";
      } else {
        loginContainer.style.display = "block";
      }
    });

    // Handle Login
    document.getElementById('login-form').addEventListener('submit', async function (e) {
      e.preventDefault();
      const email = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const errorElement = document.getElementById('error-message');
      const loadingElement = document.getElementById('loading');
      const loginButton = document.getElementById('login-button');

      errorElement.style.display = 'none';
      loadingElement.style.display = 'block';
      loginButton.disabled = true;

      try {
        const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
        await sendIdTokenToServer(userCredential.user);
      } catch (error) {
        errorElement.textContent = error.message || 'Authentication failed';
        errorElement.style.display = 'block';
        loadingElement.style.display = 'none';
        loginButton.disabled = false;
      }
    });

    // Share to Stunlo
    document.getElementById('share-button').addEventListener('click', async () => {
      const shareButton = document.getElementById('share-button');
      const loading = document.getElementById('share-loading');

      if (parsedIsMessageLaunch) {
        loading.innerText = "Sharing to Stunlo..."
      } else {
        loading.innerText = "Registering..."
      }

      shareButton.disabled = true;
      loading.style.display = 'block';

      try {
        const user = firebase.auth().currentUser;
        if (user) {
          await sendIdTokenToServer(user);
          //showSuccessNotification()
        }
      } catch (err) {
        alert("Error sharing to Stunlo: " + err.message);
      } finally {
        loading.style.display = 'none';
        shareButton.disabled = false;
      }
    });

    // Switch User
    document.getElementById('switch-button').addEventListener('click', async () => {
      await firebase.auth().signOut();
      window.location.reload();
    });
  </script>
</body>
</html>
