from functools import wraps
from flask import request, redirect, url_for, session
from firebase_config import auth

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user'):
            return redirect(url_for('login_page'))
        
        # Verify the Firebase token
        try:
            token = session['user']['idToken']
            auth.get_account_info(token)
            return f(*args, **kwargs)
        except Exception as e:
            print(f"Auth error: {e}")
            return redirect(url_for('login_page'))
            
    return decorated_function