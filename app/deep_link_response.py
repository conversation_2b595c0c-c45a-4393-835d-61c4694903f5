import json
import time
import uuid
import jwt  # PyJWT
from flask import render_template_string

class DeepLinkingResponseBuilder:
    def __init__(self, message_launch):
        self.message_launch = message_launch
        self.resources = []

    def add_resource(self, resource):
        self.resources.append(resource)

    def build_jwt(self):
        now = int(time.time())
        launch_data = self.message_launch.get_launch_data()
        deep_link_settings = launch_data.get('https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings', {})

        jwt_body = {
            "iss": self.message_launch.get_issuer(),
            "aud": self.message_launch.get_audience(),
            "nonce": str(uuid.uuid4()),
            "iat": now,
            "exp": now + 600,
            "https://purl.imsglobal.org/spec/lti/claim/message_type": "LtiDeepLinkingResponse",
            "https://purl.imsglobal.org/spec/lti/claim/version": "1.3.0",
            "https://purl.imsglobal.org/spec/lti-dl/claim/data": deep_link_settings.get("data"),
            "https://purl.imsglobal.org/spec/lti-dl/claim/content_items": [r.to_dict() for r in self.resources]
        }

        registration = self.message_launch.get_registration()
        private_key = registration.get_tool_private_key()
        kid = registration.get_kid()

        headers = {"kid": kid}

        return jwt.encode(jwt_body, private_key, algorithm='RS256', headers=headers)

    def build_form_html(self):
        jwt_token = self.build_jwt()
        return_url = self.message_launch.get_launch_data().get(
            'https://purl.imsglobal.org/spec/lti-dl/claim/deep_linking_settings', {}
        ).get('deep_link_return_url')

        return render_template_string('''
        <html>
          <body onload="document.forms[0].submit()">
            <form action="{{ return_url }}" method="POST">
              <input type="hidden" name="JWT" value="{{ jwt_token }}">
              <noscript><input type="submit" value="Continue"></noscript>
            </form>
          </body>
        </html>
        ''', jwt_token=jwt_token, return_url=return_url)
