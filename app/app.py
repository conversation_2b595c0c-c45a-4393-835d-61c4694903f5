#import datetime
import os
import pprint
import uuid
import json
import firebase_admin

from tempfile import mkdtemp
from flask import Flask, jsonify, render_template, request, url_for, Response, redirect, session
from flask_caching import Cache
#from werkzeug.exceptions import Forbidden
from pylti1p3.contrib.flask import FlaskOIDCLogin, FlaskMessageLaunch, FlaskRequest, FlaskCacheDataStorage
from pylti1p3.deep_link_resource import DeepLinkResource
#from pylti1p3.grade import Grade
#from pylti1p3.lineitem import LineItem
#from pylti1p3.tool_config import ToolConfJsonFile
from pylti1p3.registration import Registration

# Import our custom FlaskDynamicRegistration class
from dynamic_registration import FlaskDynamicRegistration
from tool_config import ToolConfig<PERSON>son, FlatToolConfigJson
from functools import wraps
from datetime import timedelta
#from firebase_config import auth as pyrebase_auth

from firebase_admin import auth as firebase_auth, credentials


cred = credentials.Certificate("serviceAccountKey.json")
firebase_admin.initialize_app(cred)

class ReverseProxied:
    def __init__(self, app):
        self.app = app

    def __call__(self, environ, start_response):
        scheme = environ.get('HTTP_X_FORWARDED_PROTO')
        if scheme:
            environ['wsgi.url_scheme'] = scheme
        return self.app(environ, start_response)


app = Flask('stunlo-tool', template_folder='templates', static_folder='static')
app.wsgi_app = ReverseProxied(app.wsgi_app)


config = {
    "DEBUG": True,
    "ENV": "development",
    "CACHE_TYPE": "simple",
    "CACHE_DEFAULT_TIMEOUT": 600,
    "SECRET_KEY": "replace-me",
    "SESSION_TYPE": "filesystem",
    "SESSION_FILE_DIR": mkdtemp(),
    "SESSION_COOKIE_NAME": "pylti1p3-flask-app-sessionid",
    "SESSION_COOKIE_HTTPONLY": True,
    "SESSION_COOKIE_SECURE": True,   # should be True in case of HTTPS usage (production)
    "SESSION_COOKIE_SAMESITE": None,  # should be 'None' in case of HTTPS usage (production)
    "DEBUG_TB_INTERCEPT_REDIRECTS": False
}
app.config.from_mapping(config)
cache = Cache(app)

PAGE_TITLE = 'Stunlo'

class ExtendedFlaskMessageLaunch(FlaskMessageLaunch):

    def validate_deployment(self):
        """
        Skip deployment ID validation by simply returning self
        instead of checking if the deployment ID is in the configured list
        """
        print("Skipping deployment ID validation")
        return self
    

def get_lti_config_path():
    return os.path.join(app.root_path, '..', 'configs', 'app.json')

def get_lti_flat_config_path():
    return os.path.join(app.root_path, '..', 'configs', 'flat_app.json')

def get_launch_data_storage():
    return FlaskCacheDataStorage(cache)


def get_jwk_from_public_key(key_name):
    key_path = os.path.join(app.root_path, '..', 'configs', key_name)
    f = open(key_path, 'r')
    key_content = f.read()
    jwk = Registration.get_jwk(key_content)
    f.close()
    return jwk


@app.route('/login', methods=['GET', 'POST'])
def login():
    launch_data_storage = get_launch_data_storage()
    tool_conf = ToolConfigJson(get_lti_config_path())
    flat_tool_conf = FlatToolConfigJson(get_lti_flat_config_path())

    flask_request = FlaskRequest()
    target_link_uri = flask_request.get_param('target_link_uri')
    
    print('---------------------')
    pprint.pprint(flask_request)
    pprint.pprint(tool_conf)

    if not target_link_uri:
        raise Exception('Missing "target_link_uri" param')

    oidc_login = FlaskOIDCLogin(flask_request, flat_tool_conf, launch_data_storage=launch_data_storage)
    return oidc_login\
        .enable_check_cookies()\
        .redirect(target_link_uri)


@app.route('/launch', methods=['POST'])
def launch():
    tool_conf = ToolConfigJson(get_lti_config_path())
    flat_tool_conf = FlatToolConfigJson(get_lti_flat_config_path())

    flask_request = FlaskRequest()
    launch_data_storage = get_launch_data_storage()
    message_launch = ExtendedFlaskMessageLaunch(flask_request, flat_tool_conf, launch_data_storage=launch_data_storage)
    message_launch_data = message_launch.get_launch_data()
    pprint.pprint(message_launch_data)
    launch_id = message_launch.get_launch_id()
    
    print("Deep Link: ", message_launch.is_deep_link_launch())

    return render_template('stunlo.html', launch_id=launch_id, is_deep_link_launch=message_launch.is_deep_link_launch(), is_message_launch=True)


@app.route('/launch-auth', methods=['POST'])
def auth():
    try:            
        request_data = request.get_json()
        id_token = request_data.get('idToken')
        launch_id = request_data.get('launch_id')
        
        flask_request = FlaskRequest()
        launch_data_storage = get_launch_data_storage()
        tool_conf = FlatToolConfigJson(get_lti_flat_config_path())
        message_launch = ExtendedFlaskMessageLaunch.from_cache(launch_id, flask_request, tool_conf, launch_data_storage=launch_data_storage)

        if not id_token:
            return jsonify({'error': 'No token provided'}), 400
        
        if not launch_id:
            return jsonify({'error': 'No launch ID provided'}), 400
    
        # Verify Firebase token
        decoded_token = firebase_auth.verify_id_token(id_token)
        uid = decoded_token['uid']
        pprint.pprint(decoded_token)
        print(f"Firebase Authenticated UID: {uid}")
        
        return handle_launch(launch_id)
        
    except Exception as e:
        print(e)
        return render_template('stunlo.html', error="Invalid credentials", launch_id=launch_id, is_deep_link_launch=message_launch.is_deep_link_launch(), is_message_launch=True)
        

def handle_launch(launch_id):
    
    tool_conf = ToolConfigJson(get_lti_config_path())
    flask_request = FlaskRequest()
    launch_data_storage = get_launch_data_storage()
    
    message_launch = ExtendedFlaskMessageLaunch.from_cache(launch_id, flask_request, tool_conf, launch_data_storage=launch_data_storage)
    message_launch_data = message_launch.get_launch_data()
    
    resource_link = message_launch_data.get('https://purl.imsglobal.org/spec/lti/claim/resource_link', {})
    resource_link_id = resource_link.get('id', 'Not provided')
    print('***************************************')
    print(f"Resource link data: {resource_link}")
    print(f"Resource link ID: {resource_link_id}")
    
    custom_params = message_launch_data.get('https://purl.imsglobal.org/spec/lti/claim/custom', {})
    
    # Log all received custom parameters
    print("Received custom parameters:")
    for key, value in custom_params.items():
        value_type = type(value).__name__  # Get type name as string, e.g., 'int' or 'str'
        print(f"  {key}: {value} (type: {value_type})")

    print('***************************************')
    
    if message_launch.is_deep_link_launch():
        print("Handling Deep Link Request from editor button")
        return handle_deep_linking(message_launch)

    #return render_template('stunlo.html')
    #return '<h1>Sent to Stunlo!</h1>'
    
    """
    <html>
        <head>
            <script>
                // Close the window after a short delay
                setTimeout(function() {
                    window.close();
                }, 1500); // close after 1.5 seconds
            </script>
        </head>
        <body>
            <h1>Sent to Stunlo!</h1>
        </body>
    </html>
    """
    return jsonify({"success": True, "message": "Content sent to Stunlo successfully"})
 
    
def handle_deep_linking(message_launch: FlaskMessageLaunch):
    resource = DeepLinkResource()
    resource.set_url(url_for('launch', _external=True))
    resource.set_type("ltiResourceLink")
    resource.set_title('Send to Stunlo')
    resource.set_custom_params(get_custom_params())
    
    return message_launch.get_deep_link().output_response_form([resource])


@app.route('/jwks', methods=['GET'])
def get_jwks():
    flat_tool_conf = FlatToolConfigJson(get_lti_flat_config_path())
    tool_conf = ToolConfigJson(get_lti_config_path())
    return flat_tool_conf.get_jwks()
#    return jsonify({'keys': tool_conf.get_jwks()})


def get_custom_params() -> dict:
    return {
        'course_id': "$Canvas.course.id",
        'user_id': "$Canvas.user.id",
        'assignment_id': "$Canvas.assignment.id",
        'api_domain': "$Canvas.api.domain",
        'user_primary_email': "$Person.email.primary",
        'user_institute_email': "$vnd.Canvas.Person.email.sis",
        'resource_link_id': "$ResourceLink.id",
        'canvas_resourse_id': "$com.instructure.Course.canvas_resource_id",
        'lti_assignment_id': "$com.instructure.Assignment.lti.id",      
#        'base_url': "$Canvas.api.baseUrl", 
#        'context_id': "$Context.id",
#        'context_uuid': "$com.instructure.Context.uuid",
#        'context_global_id': "$com.instructure.Context.globalId",
#        'account_id': "$Canvas.account.id",
#        'root_account_id': "$Canvas.rootAccount.id",
#        'root_account_global_id': "$Canvas.root_account.global_id",
#        'user_full_name': "$Person.name.full",
#        'editor_contents': "$com.instructure.Editor.contents",
#        'editor_selection': "$com.instructure.Editor.selection",
        
    }


class DynamicRegistration(FlaskDynamicRegistration):
    client_name = PAGE_TITLE

    def get_claims(self):
        return ['iss', 'sub', 'name']

    def get_scopes(self):
        return [
            'https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly',
#            'https://purl.imsglobal.org/spec/lti-ags/scope/lineitem',
#            'https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly',
#            'https://purl.imsglobal.org/spec/lti-ags/scope/score',
        ]

    def get_messages(self):
        custom_params = get_custom_params()
        #return [
            
        """{
                'type': "LtiResourceLinkRequest",
                'target_link_uri': url_for('launch', _external=True),
                'label': "Stunlo",
                'placements': [],
                'custom_parameters': custom_params
            },"""
        return [    
            {
                'type': "LtiDeepLinkingRequest",
                'target_link_uri': request.url_root.rstrip('/') + '/launch',
                'label': "Stunlo Share",
                'placements': ["editor_button"], #, "assignment_selection"],
                'custom_parameters': custom_params
            }
        ]
    
@app.route('/register', methods=['POST', 'GET'])
def register():
    reg_id = "reg-req-" + str(uuid.uuid4())
    reg_data = {
        'host': request.host,
        'url_root': request.url_root,
        'openid_configuration': request.args.get('openid_configuration'),
        'registration_token': request.args.get('registration_token') 
    }
    cache.set(reg_id, reg_data, timeout=600)
    return render_template('stunlo.html', reg_id=reg_id)

@app.route('/register-auth', methods=['POST'])
def start_registration():
    try:            
        request_data = request.get_json()
        id_token = request_data.get('idToken')
        reg_id = request_data.get('reg_id')

        if not id_token:
            return jsonify({'error': 'No token provided'}), 400

        # Verify Firebase token
        decoded_token = firebase_auth.verify_id_token(id_token)
        uid = decoded_token['uid']
        print(f"Firebase Authenticated UID: {uid}")
        
        reg_data = cache.get(reg_id)
        cache.delete(reg_id)
    
        if not reg_data:
            return "Registration data expired or missing", 400
    
        if not cache.get(reg_id):
            print("Removed successfully from cache.")
    
        print("Printing Reg DATA.........................")
        print(reg_data)
    
        reg = DynamicRegistration(get_lti_config_path(), registration_data=reg_data)    
        reg.register()
        return Response(reg.complete_html(), mimetype='text/html')
        
    except Exception as e:
        print(e)
        return render_template('stunlo.html', error="Invalid credentials", reg_id=reg_id)
             

if __name__ == '__main__':
    # app.run(host='127.0.0.1', port=9001)

    app.run(
      host='127.0.0.1',
      port=9001,
      ssl_context=("localhost.pem", "localhost-key.pem"),
      debug=True,
    )
